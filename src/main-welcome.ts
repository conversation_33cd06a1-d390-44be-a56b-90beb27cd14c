import { app, ipcMain } from 'electron';
import { ConfigManager } from './vs/welcome/electron-main/config/configManager.js';
import { WelcomeWindow } from './vs/welcome/electron-main/window/welcomeWindow.js';
import { checkFirstTimeStartup, markFirstTimeCompleted } from './vs/welcome/electron-main/startup/firstTimeStartup.js';
import { product } from './bootstrap-meta.js';
import { ShellCommandInstaller } from './vs/welcome/electron-main/utils/installCmd.js';
import * as fs from 'original-fs';
import * as path from 'path';
import { platform } from 'os';
import { promisify } from 'util';
import { exec } from 'child_process';

const isDev = process.env.NODE_ENV === 'development';

const themeIdMap = {
	'Dark-Modern': 'Default Dark Modern',
	'Light-Modern': 'Default Light Modern',
	'Tomorrow-Night-Blue': 'Tomorrow Night Blue',
};

/**
 * Check if a specific IDE (VSCode or Cursor) is installed on the system
 */
async function checkIDEInstalled(nameShort: 'code' | 'cursor'): Promise<{ isInstalled: boolean; path?: string }> {
	const platformName = platform();

	try {
		if (platformName === 'darwin') {
			return await checkIDEInstalledMacOS(nameShort);
		} else if (platformName === 'win32') {
			return await checkIDEInstalledWindows(nameShort);
		} else {
			return await checkIDEInstalledLinux(nameShort);
		}
	} catch (error) {
		console.error(`Error checking ${nameShort} installation:`, error);
		return { isInstalled: false };
	}
}

/**
 * Check IDE installation on macOS
 */
async function checkIDEInstalledMacOS(nameShort: 'code' | 'cursor'): Promise<{ isInstalled: boolean; path?: string }> {
	const appNames = {
		'code': 'Visual Studio Code.app',
		'cursor': 'Cursor.app'
	};

	const appName = appNames[nameShort];
	const commonPaths = [
		`/Applications/${appName}`,
		path.join(process.env.HOME || '', `Applications/${appName}`)
	];

	// Check common installation paths first
	for (const appPath of commonPaths) {
		try {
			const stats = await fs.promises.stat(appPath);
			if (stats.isDirectory()) {
				console.log(`Found ${nameShort} at ${appPath}`);
				return { isInstalled: true, path: appPath };
			}
		} catch {
			// Continue checking other paths
		}
	}

	// Use system_profiler for comprehensive search (slower but thorough)
	try {
		const execAsync = promisify(exec);
		const { stdout } = await execAsync('system_profiler SPApplicationsDataType -detailLevel mini');

		const lines = stdout.split('\n');
		let foundApp = false;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i].trim();
			if (line.includes(appName.replace('.app', '')) && line.endsWith(':')) {
				foundApp = true;
				continue;
			}

			if (foundApp && line.startsWith('Location:')) {
				const location = line.replace('Location:', '').trim();
				return { isInstalled: true, path: location };
			}

			// Reset if we hit another app entry
			if (foundApp && line.endsWith(':') && !line.startsWith('Location:')) {
				foundApp = false;
			}
		}
	} catch (error) {
		console.error('Error running system_profiler:', error);
	}

	return { isInstalled: false };
}

/**
 * Check IDE installation on Windows
 */
async function checkIDEInstalledWindows(nameShort: 'code' | 'cursor'): Promise<{ isInstalled: boolean; path?: string }> {
	// Check common installation paths first
	const programFiles = process.env['ProgramFiles'] || 'C:\\Program Files';
	const programFilesX86 = process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)';
	const localAppData = process.env['LOCALAPPDATA'] || '';

	const commonPaths = {
		'code': [
			path.join(programFiles, 'Microsoft VS Code', 'Code.exe'),
			path.join(programFilesX86, 'Microsoft VS Code', 'Code.exe'),
			path.join(localAppData, 'Programs', 'Microsoft VS Code', 'Code.exe')
		],
		'cursor': [
			path.join(localAppData, 'Programs', 'cursor', 'Cursor.exe'),
			path.join(programFiles, 'Cursor', 'Cursor.exe')
		]
	};

	const paths = commonPaths[nameShort];
	for (const exePath of paths) {
		try {
			const stats = await fs.promises.stat(exePath);
			if (stats.isFile()) {
				return { isInstalled: true, path: exePath };
			}
		} catch {
			// Continue checking other paths
		}
	}

	// Check Windows Registry for installation info
	try {
		const Registry = await import('@vscode/windows-registry');

		const registryPaths = {
			'code': [
				{ hive: 'HKEY_LOCAL_MACHINE' as const, path: 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{771FD6B0-FA20-440A-A002-3B3BAC16DC50}_is1', name: 'InstallLocation' },
				{ hive: 'HKEY_CURRENT_USER' as const, path: 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{771FD6B0-FA20-440A-A002-3B3BAC16DC50}_is1', name: 'InstallLocation' }
			],
			'cursor': [
				{ hive: 'HKEY_LOCAL_MACHINE' as const, path: 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Cursor', name: 'InstallLocation' },
				{ hive: 'HKEY_CURRENT_USER' as const, path: 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Cursor', name: 'InstallLocation' }
			]
		};

		for (const regPath of registryPaths[nameShort]) {
			try {
				const installLocation = Registry.GetStringRegKey(regPath.hive, regPath.path, regPath.name);
				if (installLocation) {
					const exeName = nameShort === 'code' ? 'Code.exe' : 'Cursor.exe';
					const exePath = path.join(installLocation, exeName);
					try {
						const stats = await fs.promises.stat(exePath);
						if (stats.isFile()) {
							return { isInstalled: true, path: exePath };
						}
					} catch {
						// Continue to next registry entry
					}
				}
			} catch {
				// Continue to next registry entry
			}
		}
	} catch (error) {
		console.error('Error checking Windows registry:', error);
	}

	return { isInstalled: false };
}

/**
 * Check IDE installation on Linux
 */
async function checkIDEInstalledLinux(nameShort: 'code' | 'cursor'): Promise<{ isInstalled: boolean; path?: string }> {
	const commandNames = {
		'code': ['code', 'code-insiders'],
		'cursor': ['cursor']
	};

	const commands = commandNames[nameShort];

	// Check if command is available in PATH
	for (const command of commands) {
		try {
			const execAsync = promisify(exec);
			const { stdout } = await execAsync(`which ${command}`);
			const commandPath = stdout.trim();

			if (commandPath) {
				// Verify the command actually works
				try {
					await execAsync(`${command} --version`);
					return { isInstalled: true, path: commandPath };
				} catch {
					// Command exists but doesn't work, continue checking
				}
			}
		} catch {
			// Command not found in PATH, continue checking
		}
	}

	// Check common installation directories
	const commonPaths = {
		'code': [
			'/usr/bin/code',
			'/usr/local/bin/code',
			'/opt/visual-studio-code/bin/code',
			'/snap/bin/code'
		],
		'cursor': [
			'/usr/bin/cursor',
			'/usr/local/bin/cursor',
			'/opt/cursor/cursor',
			path.join(process.env.HOME || '', '.local/bin/cursor')
		]
	};

	const paths = commonPaths[nameShort];
	for (const binPath of paths) {
		try {
			const stats = await fs.promises.stat(binPath);
			if (stats.isFile()) {
				// Check if it's executable
				try {
					await fs.promises.access(binPath, fs.constants.X_OK);
					return { isInstalled: true, path: binPath };
				} catch {
					// Not executable, continue checking
				}
			}
		} catch {
			// File doesn't exist, continue checking
		}
	}

	return { isInstalled: false };
}

export { checkFirstTimeStartup, markFirstTimeCompleted };

// Initialize IPC handlers globally - call this before showing welcome window
export function initializeWelcomeIPCHandlers() {
	const nameShort = product.nameShort ?? 'code-oss-dev';
	const configManager = new ConfigManager();

	// 导入配置
	ipcMain.handle('import-config', async (_event, nameShort: 'Code' | 'Cursor') => {
		try {
			return await configManager.importConfig(nameShort);
		} catch (error) {
			console.error('Import failed:', error);
			return {
				status: 'error',
				message: error.message,
			};
		}
	});

	// 安装命令
	ipcMain.handle('install-cli', async (_event) => {
		try {
			const installer = new ShellCommandInstaller(nameShort);
			// 安装命令
			await installer.installShellCommand();
			return {
				status: 'success',
				message: 'Installation successful',
			};
		} catch (error) {
			console.error('Install failed:', error);
			return {
				status: 'error',
				message: error.message,
			};
		}
	});

	// 登录逻辑
	ipcMain.handle('login', async (_event) => {
		try {
			const { LoginManager } = await import('./vs/welcome/electron-main/login/loginManager.js');
			const loginManager = new LoginManager(configManager.getStorageService());

			const result = await loginManager.show();

			if (result.status === 'success' && result.userInfo) {
				return { isLoggedIn: true, userInfo: result.userInfo };
			}

			throw new Error(result.message);
		} catch (error) {
			console.error('Login failed:', error);
			return { isLoggedIn: false, message: error.message };
		}
	});

	// 检查是否安装了 vscode 或 cursor
	ipcMain.handle('check-ide-installed', async (_event, nameShort: 'code' | 'cursor') => {
		try {
			const result = await checkIDEInstalled(nameShort);
			if (!result.isInstalled) throw new Error(`Failed to check ${nameShort} installation`);
			return result.isInstalled;
		} catch (error) {
			console.error(`Error checking ${nameShort} installation:`, error);
			return false;
		}
	});

	return configManager;
}

export async function showWelcomeWindow(): Promise<void> {
	const welcomeWindow = new WelcomeWindow(isDev);
	const window = await welcomeWindow.show();
	const configManager = new ConfigManager();

	// 监听欢迎流程完成事件
	window.webContents.on('ipc-message', async (_event, channel, data) => {
		// 流程结束
		if (channel === 'welcome-completed') {
			markFirstTimeCompleted();
			window.close();
			app.relaunch();
			app.exit();
		}
		// set-theme
		if (channel === 'set-theme') {
			// @ts-ignore
			await configManager.setTheme(themeIdMap[data]);
		}
	});
}
